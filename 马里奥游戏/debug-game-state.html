<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>游戏状态调试 - 马里奥游戏</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #f0f0f0;
            font-family: Arial, sans-serif;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .debug-panel {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        .debug-info {
            flex: 1;
            background: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        canvas {
            border: 2px solid #333;
            display: block;
            margin: 20px auto;
        }
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        button {
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 0 5px;
            font-size: 14px;
        }
        button:hover {
            background: #45a049;
        }
        .error {
            background: #ffebee;
            color: #c62828;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success {
            background: #e8f5e8;
            color: #2e7d32;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 马里奥游戏状态调试</h1>
        
        <div class="debug-panel">
            <div class="debug-info" id="gameState">
                <h3>游戏状态</h3>
                <div id="gameStateContent">等待检查...</div>
            </div>
            <div class="debug-info" id="objectInfo">
                <h3>游戏对象</h3>
                <div id="objectInfoContent">等待检查...</div>
            </div>
            <div class="debug-info" id="engineInfo">
                <h3>引擎状态</h3>
                <div id="engineInfoContent">等待检查...</div>
            </div>
        </div>
        
        <canvas id="gameCanvas" width="800" height="600"></canvas>
        
        <div class="controls">
            <button onclick="checkGameState()">检查游戏状态</button>
            <button onclick="startGame()">开始游戏</button>
            <button onclick="forceRender()">强制渲染</button>
            <button onclick="clearCanvas()">清空画布</button>
        </div>
        
        <div id="errorLog"></div>
    </div>

    <!-- 引入所有游戏文件 -->
    <script src="js/vector2d.js"></script>
    <script src="js/physics.js"></script>
    <script src="js/spriteManager.js"></script>
    <script src="js/gameObject.js"></script>
    <script src="js/eventManager.js"></script>
    <script src="js/audioManager.js"></script>
    <script src="js/particleSystem.js"></script>
    <script src="js/inputManager.js"></script>
    <script src="js/platform.js"></script>
    <script src="js/enemy.js"></script>
    <script src="js/collectible.js"></script>
    <script src="js/level.js"></script>
    <script src="js/scoreManager.js"></script>
    <script src="js/hudManager.js"></script>
    <script src="js/player.js"></script>
    <script src="js/gameEngine.js"></script>
    <script src="js/gameStateManager.js"></script>
    <script src="js/main.js"></script>

    <script>
        let errorLog;

        window.addEventListener('DOMContentLoaded', function() {
            errorLog = document.getElementById('errorLog');
            
            // 等待游戏初始化完成
            setTimeout(() => {
                checkGameState();
            }, 2000);
        });

        function logError(message) {
            console.error(message);
            errorLog.innerHTML += '<div class="error">❌ ' + message + '</div>';
        }

        function logSuccess(message) {
            console.log(message);
            errorLog.innerHTML += '<div class="success">✅ ' + message + '</div>';
        }

        function checkGameState() {
            const gameStateContent = document.getElementById('gameStateContent');
            const objectInfoContent = document.getElementById('objectInfoContent');
            const engineInfoContent = document.getElementById('engineInfoContent');
            
            let gameStateInfo = '';
            let objectInfo = '';
            let engineInfo = '';
            
            // 检查游戏状态管理器
            if (window.gameStateManager) {
                gameStateInfo += `状态管理器: ✅ 已创建<br>`;
                gameStateInfo += `当前状态: ${window.gameStateManager.currentState}<br>`;
                gameStateInfo += `前一状态: ${window.gameStateManager.previousState}<br>`;
            } else {
                gameStateInfo += `状态管理器: ❌ 未创建<br>`;
            }
            
            // 检查游戏引擎
            if (window.gameEngine) {
                engineInfo += `游戏引擎: ✅ 已创建<br>`;
                engineInfo += `运行状态: ${window.gameEngine.isRunning}<br>`;
                engineInfo += `游戏对象数量: ${window.gameEngine.gameObjects.length}<br>`;
                engineInfo += `FPS: ${window.gameEngine.currentFPS}<br>`;
            } else {
                engineInfo += `游戏引擎: ❌ 未创建<br>`;
            }
            
            // 检查关卡
            if (window.currentLevel) {
                objectInfo += `关卡: ✅ 已创建<br>`;
                objectInfo += `关卡已加载: ${window.currentLevel.isLoaded}<br>`;
                objectInfo += `关卡对象数量: ${window.currentLevel.allObjects.length}<br>`;
                objectInfo += `平台数量: ${window.currentLevel.platforms.length}<br>`;
                objectInfo += `敌人数量: ${window.currentLevel.enemies.length}<br>`;
                objectInfo += `收集品数量: ${window.currentLevel.collectibles.length}<br>`;
            } else {
                objectInfo += `关卡: ❌ 未创建<br>`;
            }
            
            // 检查玩家
            if (window.player) {
                objectInfo += `玩家: ✅ 已创建<br>`;
                objectInfo += `玩家位置: (${Math.round(window.player.position.x)}, ${Math.round(window.player.position.y)})<br>`;
                objectInfo += `玩家状态: ${window.player.animationState}<br>`;
            } else {
                objectInfo += `玩家: ❌ 未创建<br>`;
            }
            
            gameStateContent.innerHTML = gameStateInfo;
            objectInfoContent.innerHTML = objectInfo;
            engineInfoContent.innerHTML = engineInfo;
        }

        function startGame() {
            try {
                if (window.gameStateManager) {
                    window.gameStateManager.startGame();
                    logSuccess('游戏启动命令已发送');
                    setTimeout(checkGameState, 1000);
                } else {
                    logError('游戏状态管理器未初始化');
                }
            } catch (error) {
                logError('启动游戏失败: ' + error.message);
            }
        }

        function forceRender() {
            try {
                const canvas = document.getElementById('gameCanvas');
                const ctx = canvas.getContext('2d');
                
                // 清空画布
                ctx.fillStyle = '#87CEEB';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                // 如果有关卡，尝试渲染
                if (window.currentLevel && window.currentLevel.isLoaded) {
                    window.currentLevel.render(ctx, 0);
                    logSuccess('关卡渲染完成');
                } else if (window.gameEngine) {
                    window.gameEngine.renderAllObjects(0);
                    logSuccess('游戏对象渲染完成');
                } else {
                    logError('没有可渲染的内容');
                }
            } catch (error) {
                logError('强制渲染失败: ' + error.message);
            }
        }

        function clearCanvas() {
            const canvas = document.getElementById('gameCanvas');
            const ctx = canvas.getContext('2d');
            ctx.fillStyle = '#87CEEB';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
        }

        // 错误监听
        window.addEventListener('error', function(event) {
            logError('JavaScript错误: ' + event.error.message);
        });
    </script>
</body>
</html>
