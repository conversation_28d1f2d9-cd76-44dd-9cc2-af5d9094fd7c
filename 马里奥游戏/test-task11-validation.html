<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务11验证 - 游戏状态管理</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f0f0f0;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .pass { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .fail { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .controls {
            margin: 20px 0;
            padding: 15px;
            background: #e9ecef;
            border-radius: 5px;
        }
        .controls button {
            margin: 5px;
            padding: 10px 15px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-info { background: #17a2b8; color: white; }
        .game-container {
            text-align: center;
            margin: 20px 0;
        }
        #gameCanvas {
            border: 2px solid #333;
            border-radius: 5px;
        }
        .status-display {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <h1>任务11验证 - 游戏状态管理系统</h1>
    
    <div class="test-container">
        <h2>测试说明</h2>
        <p>本测试验证游戏状态管理系统的各项功能：</p>
        <ul>
            <li>GameStateManager类的创建和初始化</li>
            <li>游戏开始菜单界面</li>
            <li>游戏结束和胜利界面</li>
            <li>暂停和恢复功能</li>
            <li>重新开始游戏逻辑</li>
            <li>状态转换和UI更新</li>
        </ul>
    </div>

    <div class="test-container">
        <h2>游戏状态管理器测试</h2>
        <div id="gameStateTests"></div>
        
        <div class="controls">
            <h3>手动测试控制</h3>
            <button class="btn-primary" onclick="testStartGame()">开始游戏</button>
            <button class="btn-warning" onclick="testPauseGame()">暂停游戏</button>
            <button class="btn-success" onclick="testResumeGame()">恢复游戏</button>
            <button class="btn-danger" onclick="testGameOver()">触发游戏结束</button>
            <button class="btn-info" onclick="testVictory()">触发胜利</button>
            <button class="btn-primary" onclick="testReturnToMenu()">返回菜单</button>
        </div>
        
        <div class="status-display">
            <div><strong>当前状态:</strong> <span id="currentState">未初始化</span></div>
            <div><strong>分数:</strong> <span id="currentScore">0</span></div>
            <div><strong>生命:</strong> <span id="currentLives">3</span></div>
            <div><strong>关卡:</strong> <span id="currentLevel">1</span></div>
        </div>
    </div>

    <div class="test-container">
        <h2>游戏界面</h2>
        <div class="game-container">
            <div class="game-header">
                <h3>马里奥游戏</h3>
                <div class="game-info">
                    <span id="score">分数: 0</span>
                    <span id="lives">生命: 3</span>
                </div>
            </div>
            
            <canvas id="gameCanvas" width="800" height="600"></canvas>
            
            <div class="game-controls">
                <p>控制说明：← → 方向键移动，空格键或↑跳跃，ESC暂停</p>
            </div>
            
            <!-- 游戏菜单 -->
            <div class="game-menu" id="gameMenu" style="display: none; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: rgba(255,255,255,0.95); padding: 40px; border-radius: 10px; box-shadow: 0 10px 30px rgba(0,0,0,0.3); text-align: center; z-index: 100;">
                <h2 style="color: #FF6B35; margin-bottom: 20px;">开始游戏</h2>
                <button id="startButton" style="background: #FF6B35; color: white; border: none; padding: 15px 30px; font-size: 1.2em; border-radius: 5px; cursor: pointer; margin: 10px;">开始</button>
                <button id="pauseButton" style="background: #FFA500; color: white; border: none; padding: 15px 30px; font-size: 1.2em; border-radius: 5px; cursor: pointer; margin: 10px; display: none;">暂停</button>
            </div>
            
            <!-- 游戏结束界面 -->
            <div class="game-over" id="gameOver" style="display: none; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: rgba(255,255,255,0.95); padding: 40px; border-radius: 10px; box-shadow: 0 10px 30px rgba(0,0,0,0.3); text-align: center; z-index: 100;">
                <h2 style="color: #FF6B35; margin-bottom: 20px;">游戏结束</h2>
                <p>最终分数: <span id="finalScore">0</span></p>
                <button id="restartButton" style="background: #FF6B35; color: white; border: none; padding: 15px 30px; font-size: 1.2em; border-radius: 5px; cursor: pointer; margin: 10px;">重新开始</button>
                <button class="menu-button" style="background: #808080; color: white; border: none; padding: 15px 30px; font-size: 1.2em; border-radius: 5px; cursor: pointer; margin: 10px;">返回菜单</button>
            </div>
            
            <!-- 胜利界面 -->
            <div class="victory-screen" id="victoryScreen" style="display: none; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: rgba(255,255,255,0.95); padding: 40px; border-radius: 10px; box-shadow: 0 10px 30px rgba(0,0,0,0.3); text-align: center; z-index: 100;">
                <h2 style="color: #FFD700; margin-bottom: 20px;">恭喜通关！</h2>
                <p>关卡分数: <span id="victoryScore">0</span></p>
                <button id="nextLevelButton" style="background: #32CD32; color: white; border: none; padding: 15px 30px; font-size: 1.2em; border-radius: 5px; cursor: pointer; margin: 10px;">下一关</button>
                <button class="menu-button" style="background: #808080; color: white; border: none; padding: 15px 30px; font-size: 1.2em; border-radius: 5px; cursor: pointer; margin: 10px;">返回菜单</button>
            </div>
            
            <!-- 暂停界面 -->
            <div class="pause-overlay" id="pauseOverlay" style="display: none; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: rgba(255,255,255,0.95); padding: 40px; border-radius: 10px; box-shadow: 0 10px 30px rgba(0,0,0,0.3); text-align: center; z-index: 100;">
                <h2 style="color: #4169E1; margin-bottom: 20px;">游戏暂停</h2>
                <button id="continueButton" style="background: #4169E1; color: white; border: none; padding: 15px 30px; font-size: 1.2em; border-radius: 5px; cursor: pointer; margin: 10px;">继续游戏</button>
                <button class="menu-button" style="background: #808080; color: white; border: none; padding: 15px 30px; font-size: 1.2em; border-radius: 5px; cursor: pointer; margin: 10px;">返回菜单</button>
            </div>
        </div>
    </div>

    <div class="test-container">
        <h2>自动化测试结果</h2>
        <div id="testResults"></div>
        <button class="btn-primary" onclick="runAllTests()">运行所有测试</button>
    </div>

    <!-- 加载游戏脚本 -->
    <script src="js/vector2d.js"></script>
    <script src="js/physics.js"></script>
    <script src="js/spriteManager.js"></script>
    <script src="js/gameObject.js"></script>
    <script src="js/inputManager.js"></script>
    <script src="js/platform.js"></script>
    <script src="js/enemy.js"></script>
    <script src="js/collectible.js"></script>
    <script src="js/level.js"></script>
    <script src="js/scoreManager.js"></script>
    <script src="js/hudManager.js"></script>
    <script src="js/player.js"></script>
    <script src="js/gameEngine.js"></script>
    <script src="js/gameStateManager.js"></script>
    <script src="js/main.js"></script>

    <script>
        // 测试变量
        let testGameStateManager;
        let testGameEngine;
        let testCanvas;
        
        // 初始化测试
        function initTests() {
            testCanvas = document.getElementById('gameCanvas');
            testGameEngine = new GameEngine(testCanvas);
            testGameEngine.init();
            
            testGameStateManager = new GameStateManager(testGameEngine, testCanvas);
            testGameStateManager.init();
            
            // 设置状态变化监听
            testGameStateManager.onStateChange((newState, oldState) => {
                updateStatusDisplay();
            });
            
            updateStatusDisplay();
        }
        
        // 更新状态显示
        function updateStatusDisplay() {
            if (testGameStateManager) {
                document.getElementById('currentState').textContent = testGameStateManager.getCurrentState();
                const gameData = testGameStateManager.getGameData();
                document.getElementById('currentScore').textContent = gameData.score;
                document.getElementById('currentLives').textContent = gameData.lives;
                document.getElementById('currentLevel').textContent = gameData.level;
            }
        }
        
        // 手动测试函数
        function testStartGame() {
            if (testGameStateManager) {
                testGameStateManager.startGame();
                addTestResult('手动测试', '开始游戏', 'pass', '游戏状态已切换到PLAYING');
            }
        }
        
        function testPauseGame() {
            if (testGameStateManager) {
                testGameStateManager.pauseGame();
                addTestResult('手动测试', '暂停游戏', 'pass', '游戏状态已切换到PAUSED');
            }
        }
        
        function testResumeGame() {
            if (testGameStateManager) {
                testGameStateManager.resumeGame();
                addTestResult('手动测试', '恢复游戏', 'pass', '游戏状态已切换到PLAYING');
            }
        }
        
        function testGameOver() {
            if (testGameStateManager) {
                testGameStateManager.gameOver();
                addTestResult('手动测试', '游戏结束', 'pass', '游戏状态已切换到GAME_OVER');
            }
        }
        
        function testVictory() {
            if (testGameStateManager) {
                testGameStateManager.victory();
                addTestResult('手动测试', '游戏胜利', 'pass', '游戏状态已切换到VICTORY');
            }
        }
        
        function testReturnToMenu() {
            if (testGameStateManager) {
                testGameStateManager.returnToMenu();
                addTestResult('手动测试', '返回菜单', 'pass', '游戏状态已切换到MENU');
            }
        }
        
        // 运行所有自动化测试
        function runAllTests() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '';
            
            // 测试1: GameStateManager类存在
            addTestResult('类存在性', 'GameStateManager类', 
                typeof GameStateManager === 'function' ? 'pass' : 'fail',
                typeof GameStateManager === 'function' ? 'GameStateManager类已定义' : 'GameStateManager类未找到'
            );
            
            // 测试2: GameState枚举存在
            addTestResult('枚举存在性', 'GameState枚举', 
                typeof GameState === 'object' ? 'pass' : 'fail',
                typeof GameState === 'object' ? 'GameState枚举已定义' : 'GameState枚举未找到'
            );
            
            // 测试3: 状态管理器初始化
            try {
                const canvas = document.createElement('canvas');
                const engine = new GameEngine(canvas);
                const stateManager = new GameStateManager(engine, canvas);
                stateManager.init();
                
                addTestResult('初始化', '状态管理器初始化', 'pass', '状态管理器成功初始化');
            } catch (error) {
                addTestResult('初始化', '状态管理器初始化', 'fail', `初始化失败: ${error.message}`);
            }
            
            // 测试4: 状态转换
            if (testGameStateManager) {
                const initialState = testGameStateManager.getCurrentState();
                testGameStateManager.setState(GameState.PLAYING);
                const newState = testGameStateManager.getCurrentState();
                
                addTestResult('状态转换', '状态切换功能', 
                    newState === GameState.PLAYING ? 'pass' : 'fail',
                    `状态从 ${initialState} 切换到 ${newState}`
                );
            }
            
            // 测试5: UI元素存在性
            const requiredElements = ['gameMenu', 'gameOver', 'victoryScreen', 'pauseOverlay'];
            let uiTestPassed = true;
            let uiTestMessage = '';
            
            for (const elementId of requiredElements) {
                const element = document.getElementById(elementId);
                if (!element) {
                    uiTestPassed = false;
                    uiTestMessage += `${elementId} 元素未找到; `;
                }
            }
            
            addTestResult('UI元素', '必需UI元素存在性', 
                uiTestPassed ? 'pass' : 'fail',
                uiTestPassed ? '所有必需UI元素都存在' : uiTestMessage
            );
            
            // 测试6: 按钮功能
            const buttons = ['startButton', 'pauseButton', 'restartButton', 'continueButton', 'nextLevelButton'];
            let buttonTestPassed = true;
            let buttonTestMessage = '';
            
            for (const buttonId of buttons) {
                const button = document.getElementById(buttonId);
                if (!button) {
                    buttonTestPassed = false;
                    buttonTestMessage += `${buttonId} 按钮未找到; `;
                }
            }
            
            addTestResult('按钮功能', '游戏控制按钮存在性', 
                buttonTestPassed ? 'pass' : 'fail',
                buttonTestPassed ? '所有游戏控制按钮都存在' : buttonTestMessage
            );
            
            // 测试7: 游戏数据管理
            if (testGameStateManager) {
                const gameData = testGameStateManager.getGameData();
                const hasRequiredData = gameData.hasOwnProperty('score') && 
                                       gameData.hasOwnProperty('lives') && 
                                       gameData.hasOwnProperty('level');
                
                addTestResult('数据管理', '游戏数据结构', 
                    hasRequiredData ? 'pass' : 'fail',
                    hasRequiredData ? '游戏数据结构完整' : '游戏数据结构不完整'
                );
            }
            
            // 测试8: 本地存储功能
            try {
                if (testGameStateManager) {
                    testGameStateManager.saveGameStats();
                    const highScore = testGameStateManager.getHighScore();
                    
                    addTestResult('本地存储', '游戏统计保存', 'pass', 
                        `游戏统计保存成功，当前最高分: ${highScore}`);
                }
            } catch (error) {
                addTestResult('本地存储', '游戏统计保存', 'fail', 
                    `本地存储失败: ${error.message}`);
            }
        }
        
        // 添加测试结果
        function addTestResult(category, testName, result, message) {
            const resultsDiv = document.getElementById('testResults');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${result}`;
            resultDiv.innerHTML = `
                <strong>[${category}] ${testName}:</strong> 
                <span style="text-transform: uppercase;">${result}</span> - ${message}
            `;
            resultsDiv.appendChild(resultDiv);
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(initTests, 1000); // 延迟初始化确保所有脚本加载完成
        });
    </script>
</body>
</html>