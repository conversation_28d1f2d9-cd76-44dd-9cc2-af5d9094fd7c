<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>渲染测试 - 马里奥游戏调试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #f0f0f0;
            font-family: Arial, sans-serif;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        canvas {
            border: 2px solid #333;
            display: block;
            margin: 20px auto;
        }
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        button {
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 0 10px;
            font-size: 16px;
        }
        button:hover {
            background: #45a049;
        }
        .debug-info {
            background: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 14px;
        }
        .error {
            background: #ffebee;
            color: #c62828;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success {
            background: #e8f5e8;
            color: #2e7d32;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 马里奥游戏渲染测试</h1>
        <p>这个页面用于测试游戏的基本渲染功能</p>
        
        <canvas id="testCanvas" width="800" height="600"></canvas>
        
        <div class="controls">
            <button onclick="testBasicRender()">测试基本渲染</button>
            <button onclick="testPlatformRender()">测试平台渲染</button>
            <button onclick="testPlayerRender()">测试玩家渲染</button>
            <button onclick="clearCanvas()">清空画布</button>
        </div>
        
        <div id="debugInfo" class="debug-info">
            等待测试...
        </div>
        
        <div id="errorLog"></div>
    </div>

    <!-- 引入必要的游戏文件 -->
    <script src="js/vector2d.js"></script>
    <script src="js/physics.js"></script>
    <script src="js/gameObject.js"></script>
    <script src="js/platform.js"></script>
    <script src="js/player.js"></script>

    <script>
        let canvas, ctx;
        let debugInfo, errorLog;

        // 初始化
        window.addEventListener('DOMContentLoaded', function() {
            canvas = document.getElementById('testCanvas');
            ctx = canvas.getContext('2d');
            debugInfo = document.getElementById('debugInfo');
            errorLog = document.getElementById('errorLog');
            
            log('✅ 页面加载完成');
            log('✅ Canvas获取成功: ' + (ctx ? '是' : '否'));
            
            // 检查必要的类是否存在
            checkClasses();
        });

        function log(message) {
            console.log(message);
            debugInfo.innerHTML += message + '<br>';
        }

        function logError(message) {
            console.error(message);
            errorLog.innerHTML += '<div class="error">❌ ' + message + '</div>';
        }

        function logSuccess(message) {
            console.log(message);
            errorLog.innerHTML += '<div class="success">✅ ' + message + '</div>';
        }

        function checkClasses() {
            const classes = ['Vector2D', 'GameObject', 'Platform', 'Player'];
            let allPresent = true;
            
            classes.forEach(className => {
                if (typeof window[className] !== 'undefined') {
                    log(`✅ ${className} 类已加载`);
                } else {
                    logError(`${className} 类未找到`);
                    allPresent = false;
                }
            });
            
            if (allPresent) {
                logSuccess('所有必要的类都已正确加载');
            }
        }

        function clearCanvas() {
            ctx.fillStyle = '#87CEEB';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            log('🧹 画布已清空');
        }

        function testBasicRender() {
            try {
                clearCanvas();
                
                // 绘制一些基本图形
                ctx.fillStyle = '#FF0000';
                ctx.fillRect(100, 100, 50, 50);
                
                ctx.fillStyle = '#00FF00';
                ctx.fillRect(200, 200, 50, 50);
                
                ctx.fillStyle = '#0000FF';
                ctx.fillRect(300, 300, 50, 50);
                
                ctx.fillStyle = '#FFFFFF';
                ctx.font = '20px Arial';
                ctx.fillText('基本渲染测试成功！', 50, 50);
                
                logSuccess('基本渲染测试通过');
            } catch (error) {
                logError('基本渲染测试失败: ' + error.message);
            }
        }

        function testPlatformRender() {
            try {
                clearCanvas();
                
                if (typeof Platform === 'undefined') {
                    logError('Platform类未定义');
                    return;
                }
                
                // 创建一个平台对象
                const platform = new Platform(100, 500, 200, 50, 'ground');
                
                // 手动设置渲染上下文
                ctx.save();
                ctx.translate(platform.position.x + platform.size.x/2, platform.position.y + platform.size.y/2);
                
                // 调用平台的渲染方法
                platform.onRender(ctx, 0);
                
                ctx.restore();
                
                ctx.fillStyle = '#FFFFFF';
                ctx.font = '16px Arial';
                ctx.fillText('平台渲染测试', 50, 50);
                
                logSuccess('平台渲染测试通过');
            } catch (error) {
                logError('平台渲染测试失败: ' + error.message);
                console.error(error);
            }
        }

        function testPlayerRender() {
            try {
                clearCanvas();
                
                if (typeof Player === 'undefined') {
                    logError('Player类未定义');
                    return;
                }
                
                // 创建一个玩家对象
                const player = new Player(400, 300);
                
                // 手动设置渲染上下文
                ctx.save();
                ctx.translate(player.position.x + player.size.x/2, player.position.y + player.size.y/2);
                
                // 调用玩家的渲染方法
                player.onRender(ctx, 0);
                
                ctx.restore();
                
                ctx.fillStyle = '#FFFFFF';
                ctx.font = '16px Arial';
                ctx.fillText('玩家渲染测试', 50, 50);
                
                logSuccess('玩家渲染测试通过');
            } catch (error) {
                logError('玩家渲染测试失败: ' + error.message);
                console.error(error);
            }
        }

        // 错误监听
        window.addEventListener('error', function(event) {
            logError('JavaScript错误: ' + event.error.message);
            logError('文件: ' + event.filename + ':' + event.lineno);
        });
    </script>
</body>
</html>
