<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最终测试 - 马里奥游戏</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div class="game-container">
        <div class="game-header">
            <h1>马里奥游戏 - 最终测试</h1>
            <div class="game-info">
                <span id="score">分数: 0</span>
                <span id="lives">生命: 3</span>
            </div>
        </div>
        
        <canvas id="gameCanvas" width="800" height="600"></canvas>
        
        <div class="game-controls">
            <p>控制说明：</p>
            <p>← → 方向键移动，空格键或↑跳跃</p>
        </div>
        
        <div class="game-menu" id="gameMenu" style="display: block; z-index: 1000;">
            <h2>🎮 马里奥游戏</h2>
            <button id="startButton" style="background: #FF6B35; color: white; padding: 15px 30px; font-size: 1.2em; border: none; border-radius: 5px; cursor: pointer;">🚀 开始游戏</button>
            <button id="pauseButton" style="display: none;">暂停</button>
            <p style="margin-top: 15px; color: #666;">点击开始按钮开始游戏！</p>
        </div>
        
        <div class="game-over" id="gameOver" style="display: none;">
            <h2>游戏结束</h2>
            <p>最终分数: <span id="finalScore">0</span></p>
            <button id="restartButton">重新开始</button>
            <button class="menu-button">返回菜单</button>
        </div>
        
        <div class="victory-screen" id="victoryScreen" style="display: none;">
            <h2>恭喜通关！</h2>
            <p>关卡分数: <span id="victoryScore">0</span></p>
            <button id="nextLevelButton">下一关</button>
            <button class="menu-button">返回菜单</button>
        </div>
        
        <div class="pause-overlay" id="pauseOverlay" style="display: none;">
            <h2>游戏暂停</h2>
            <button id="continueButton">继续游戏</button>
            <button class="menu-button">返回菜单</button>
        </div>
        
        <!-- 调试信息面板 -->
        <div style="position: fixed; top: 10px; right: 10px; background: rgba(0,0,0,0.8); color: white; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 12px; max-width: 300px;">
            <div id="debugInfo">
                <strong>调试信息:</strong><br>
                <span id="debugStatus">等待初始化...</span>
            </div>
        </div>
    </div>
    
    <script src="js/vector2d.js"></script>
    <script src="js/physics.js"></script>
    <script src="js/spriteManager.js"></script>
    <script src="js/gameObject.js"></script>
    <script src="js/eventManager.js"></script>
    <script src="js/audioManager.js"></script>
    <script src="js/particleSystem.js"></script>
    <script src="js/inputManager.js"></script>
    <script src="js/platform.js"></script>
    <script src="js/enemy.js"></script>
    <script src="js/collectible.js"></script>
    <script src="js/level.js"></script>
    <script src="js/scoreManager.js"></script>
    <script src="js/hudManager.js"></script>
    <script src="js/player.js"></script>
    <script src="js/gameEngine.js"></script>
    <script src="js/gameStateManager.js"></script>
    <script src="js/main.js"></script>

    <script>
        // 调试信息更新
        function updateDebugInfo() {
            const debugStatus = document.getElementById('debugStatus');
            let info = '';
            
            if (window.gameEngine) {
                info += `引擎运行: ${window.gameEngine.isRunning}<br>`;
                info += `游戏对象: ${window.gameEngine.gameObjects.length}<br>`;
            }
            
            if (window.currentLevel) {
                info += `关卡加载: ${window.currentLevel.isLoaded}<br>`;
                info += `关卡对象: ${window.currentLevel.allObjects.length}<br>`;
            }
            
            if (window.player) {
                info += `玩家位置: (${Math.round(window.player.position.x)}, ${Math.round(window.player.position.y)})<br>`;
            }
            
            if (window.gameStateManager) {
                info += `游戏状态: ${window.gameStateManager.currentState}<br>`;
            }
            
            debugStatus.innerHTML = info || '等待初始化...';
        }
        
        // 定期更新调试信息
        setInterval(updateDebugInfo, 1000);
        
        // 监听游戏开始按钮
        document.addEventListener('DOMContentLoaded', function() {
            const startButton = document.getElementById('startButton');
            if (startButton) {
                startButton.addEventListener('click', function() {
                    console.log('🎮 开始按钮被点击！');
                    setTimeout(updateDebugInfo, 500);
                });
            }
        });
        
        // 错误监听
        window.addEventListener('error', function(event) {
            console.error('❌ 错误:', event.error.message);
            const debugStatus = document.getElementById('debugStatus');
            debugStatus.innerHTML = `❌ 错误: ${event.error.message}`;
        });
        
        console.log('🎮 最终测试页面加载完成');
    </script>
</body>
</html>
